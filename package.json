{"name": "kilatjs", "version": "1.0.0", "description": "⚡ Framework Glow Futuristik Nusantara - Modular, Fast, and Beautiful UI Framework for Web, Desktop & Mobile", "keywords": ["framework", "ui", "glow", "futuristic", "nusantara", "cyberpunk", "3d-animation", "monorepo", "typescript", "react", "vite", "electron", "expo"], "author": "Kilat.js Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/kangpcode/kilatjs.git"}, "homepage": "https://kilatjs.dev", "bugs": {"url": "https://github.com/kangpcode/kilatjs/issues"}, "workspaces": ["packages/*", "apps/*"], "scripts": {"dev": "bun run --filter='./apps/*' dev", "build": "bun run --filter='./packages/*' build", "build:apps": "bun run --filter='./apps/*' build", "test": "bun test", "test:packages": "bun run --filter='./packages/*' test", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write .", "clean": "bun run --filter='./packages/*' clean && bun run --filter='./apps/*' clean", "publish:packages": "bun run --filter='./packages/*' publish", "kilat": "bun run packages/kilat-cli/bin/kilat.js", "docs:dev": "cd docs && bun run dev", "docs:build": "cd docs && bun run build", "setup": "bun install && bun run build"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.0", "typescript": "^5.3.0", "bun-types": "^1.0.0"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}, "packageManager": "bun@1.0.0"}